import { Controller } from '@hotwired/stimulus';

export default class extends Controller {
    static targets = ['form', 'results', 'count', 'categoryRadio', 'breedCheckbox', 'regionCheckbox', 'typeCheckbox', 'sidebar'];
    static values = {
        baseUrl: String,
        currentCategory: String
    };

    connect() {
        this.setupEventListeners();
        this.updateBreedOptions();
    }

    disconnect() {
        this.removeEventListeners();
    }

    setupEventListeners() {
        // Odstranit existující listenery (pokud existují)
        this.removeEventListeners();

        // Poslouchání změn v kategoriích
        this.categoryRadioTargets.forEach(radio => {
            const handler = () => this.onCategoryChange();
            radio.addEventListener('change', handler);
            // Uložit handler pro pozdější odstranění
            radio._filterHandler = handler;
        });

        // Poslouchání změn v ostatních filtrech
        [...this.breedCheckboxTargets, ...this.regionCheckboxTargets, ...this.typeCheckboxTargets].forEach(checkbox => {
            const handler = () => this.onFilterChange();
            checkbox.addEventListener('change', handler);
            checkbox._filterHandler = handler;
        });

        // Poslouchání změn v cenových polích
        const priceInputs = this.element.querySelectorAll('input[name="priceFrom"], input[name="priceTo"]');
        priceInputs.forEach(input => {
            const handler = this.debounce(() => this.onFilterChange(), 500);
            input.addEventListener('input', handler);
            input._filterHandler = handler;
        });

        // Poslouchání změn ve vyhledávacím poli
        const searchInput = this.element.querySelector('input[name="search"]');
        if (searchInput) {
            const handler = this.debounce(() => this.onFilterChange(), 300);
            searchInput.addEventListener('input', handler);
            searchInput._filterHandler = handler;
        }
    }

    removeEventListeners() {
        // Odstranit listenery z kategorií
        this.categoryRadioTargets.forEach(radio => {
            if (radio._filterHandler) {
                radio.removeEventListener('change', radio._filterHandler);
                delete radio._filterHandler;
            }
        });

        // Odstranit listenery z ostatních filtrů
        [...this.breedCheckboxTargets, ...this.regionCheckboxTargets, ...this.typeCheckboxTargets].forEach(checkbox => {
            if (checkbox._filterHandler) {
                checkbox.removeEventListener('change', checkbox._filterHandler);
                delete checkbox._filterHandler;
            }
        });

        // Odstranit listenery z cenových polí
        const priceInputs = this.element.querySelectorAll('input[name="priceFrom"], input[name="priceTo"]');
        priceInputs.forEach(input => {
            if (input._filterHandler) {
                input.removeEventListener('input', input._filterHandler);
                delete input._filterHandler;
            }
        });

        // Odstranit listener z vyhledávacího pole
        const searchInput = this.element.querySelector('input[name="search"]');
        if (searchInput && searchInput._filterHandler) {
            searchInput.removeEventListener('input', searchInput._filterHandler);
            delete searchInput._filterHandler;
        }
    }

    onCategoryChange() {
        const selectedCategory = this.getSelectedCategory();

        // Aktualizovat URL
        let newUrl = this.baseUrlValue;
        if (selectedCategory && selectedCategory !== 'all') {
            newUrl += '/' + selectedCategory;
        }

        // Vyčistit filtry plemen při změně kategorie
        this.clearBreedFilters();

        // Aktualizovat možnosti plemen
        this.updateBreedOptions();

        // Přesměrovat na novou URL
        window.location.href = newUrl + this.buildQueryString();
    }

    onFilterChange() {
        const url = this.buildFilterUrl();

        // Aktualizovat URL bez reload stránky
        window.history.pushState({}, '', url);

        // Načíst nové výsledky pomocí fetch
        this.loadResults(url);
    }

    getSelectedCategory() {
        const selectedRadio = this.categoryRadioTargets.find(radio => radio.checked);
        return selectedRadio ? selectedRadio.value : null;
    }

    clearBreedFilters() {
        this.breedCheckboxTargets.forEach(checkbox => {
            checkbox.checked = false;
        });
    }

    updateBreedOptions() {
        const selectedCategory = this.getSelectedCategory();

        this.breedCheckboxTargets.forEach(checkbox => {
            const breedCategory = checkbox.dataset.category;
            const filterOption = checkbox.closest('.filter-option');

            if (selectedCategory === 'all' || !selectedCategory) {
                // Zobrazit všechna plemena
                filterOption.style.display = 'block';
            } else {
                // Zobrazit pouze plemena vybrané kategorie
                if (breedCategory === selectedCategory) {
                    filterOption.style.display = 'block';
                } else {
                    filterOption.style.display = 'none';
                    // Odznačit skryté checkboxy pouze pokud nejsou disabled
                    if (!checkbox.disabled) {
                        checkbox.checked = false;
                    }
                }
            }
        });
    }

    buildFilterUrl() {
        let url = this.baseUrlValue;

        // Přidat kategorii do URL
        const selectedCategory = this.getSelectedCategory();
        if (selectedCategory && selectedCategory !== 'all') {
            url += '/' + selectedCategory;
        }

        url += this.buildQueryString();
        return url;
    }

    buildQueryString() {
        const params = new URLSearchParams();

        // Plemena
        this.breedCheckboxTargets.forEach(checkbox => {
            if (checkbox.checked && !checkbox.disabled && checkbox.closest('.filter-option').style.display !== 'none') {
                params.append('breeds[]', checkbox.value);
            }
        });

        // Kraje
        this.regionCheckboxTargets.forEach(checkbox => {
            if (checkbox.checked && !checkbox.disabled) {
                params.append('regions[]', checkbox.value);
            }
        });

        // Typy inzerátů
        this.typeCheckboxTargets.forEach(checkbox => {
            if (checkbox.checked && !checkbox.disabled) {
                params.append('types[]', checkbox.value);
            }
        });

        // Ceny
        const priceFrom = this.element.querySelector('input[name="priceFrom"]')?.value;
        const priceTo = this.element.querySelector('input[name="priceTo"]')?.value;

        if (priceFrom) {
            params.append('priceFrom', priceFrom);
        }

        if (priceTo) {
            params.append('priceTo', priceTo);
        }

        // Vyhledávání
        const search = this.element.querySelector('input[name="search"]')?.value;
        if (search) {
            params.append('search', search);
        }

        const queryString = params.toString();
        return queryString ? '?' + queryString : '';
    }

    async loadResults(url) {
        try {
            // Zobrazit loading indikátor
            this.showLoading();

            const response = await fetch(url, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            if (response.ok) {
                const html = await response.text();

                // Parsovat HTML a aktualizovat výsledky i sidebar
                const parser = new DOMParser();
                const doc = parser.parseFromString(html, 'text/html');

                const newResults = doc.querySelector('[data-filter-target="results"]');
                const newCount = doc.querySelector('[data-filter-target="count"]');
                const newSidebar = doc.querySelector('[data-filter-target="sidebar"]');

                if (newResults && this.hasResultsTarget) {
                    this.resultsTarget.innerHTML = newResults.innerHTML;
                }

                if (newCount && this.hasCountTarget) {
                    this.countTarget.innerHTML = newCount.innerHTML;
                }

                if (newSidebar && this.hasSidebarTarget) {
                    this.sidebarTarget.innerHTML = newSidebar.innerHTML;
                    // Po aktualizaci sidebaru musíme znovu nastavit event listenery
                    this.setupEventListeners();
                    this.updateBreedOptions();
                }
            }
        } catch (error) {
            console.error('Chyba při načítání výsledků:', error);
        } finally {
            this.hideLoading();
        }
    }

    showLoading() {
        if (this.hasResultsTarget) {
            this.resultsTarget.style.opacity = '0.5';
            this.resultsTarget.style.pointerEvents = 'none';
        }
    }

    hideLoading() {
        if (this.hasResultsTarget) {
            this.resultsTarget.style.opacity = '1';
            this.resultsTarget.style.pointerEvents = 'auto';
        }
    }

    clearAllFilters() {
        // Vyčistit všechny checkboxy
        [...this.breedCheckboxTargets, ...this.regionCheckboxTargets, ...this.typeCheckboxTargets].forEach(checkbox => {
            checkbox.checked = false;
        });

        // Vyčistit cenová pole
        const priceInputs = this.element.querySelectorAll('input[name="priceFrom"], input[name="priceTo"]');
        priceInputs.forEach(input => {
            input.value = '';
        });

        // Vyčistit vyhledávací pole
        const searchInput = this.element.querySelector('input[name="search"]');
        if (searchInput) {
            searchInput.value = '';
        }

        // Nastavit kategorii na "Všechny"
        const allCategoryRadio = this.categoryRadioTargets.find(radio => radio.value === 'all');
        if (allCategoryRadio) {
            allCategoryRadio.checked = true;
        }

        // Aktualizovat filtry
        this.onCategoryChange();
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}
