import { Controller } from '@hotwired/stimulus';

export default class extends Controller {
    static targets = ['categoryRadio', 'breedCheckbox', 'regionCheckbox', 'typeCheckbox'];
    static values = {
        baseUrl: String,
        currentCategory: String
    };

    connect() {
        this.setupEventListeners();
        this.updateBreedOptions();
    }

    setupEventListeners() {
        // Poslouchání změn v kategoriích
        this.categoryRadioTargets.forEach(radio => {
            radio.addEventListener('change', () => {
                this.onCategoryChange();
            });
        });

        // Poslouchání změn v ostatních filtrech
        [...this.breedCheckboxTargets, ...this.regionCheckboxTargets, ...this.typeCheckboxTargets].forEach(checkbox => {
            checkbox.addEventListener('change', () => {
                this.onFilterChange();
            });
        });

        // Poslouchání změn v cenových polích
        const priceInputs = this.element.querySelectorAll('input[name="priceFrom"], input[name="priceTo"]');
        priceInputs.forEach(input => {
            input.addEventListener('input', this.debounce(() => {
                this.onFilterChange();
            }, 500));
        });

        // Poslouchání změn ve vyhledávacím poli
        const searchInput = this.element.querySelector('input[name="search"]');
        if (searchInput) {
            searchInput.addEventListener('input', this.debounce(() => {
                this.onFilterChange();
            }, 300));
        }
    }

    onCategoryChange() {
        const selectedCategory = this.getSelectedCategory();

        // Aktualizovat URL
        let newUrl = this.baseUrlValue;
        if (selectedCategory && selectedCategory !== 'all') {
            newUrl += '/' + selectedCategory;
        }

        // Vyčistit filtry plemen při změně kategorie
        this.clearBreedFilters();

        // Aktualizovat možnosti plemen
        this.updateBreedOptions();

        // Přesměrovat na novou URL
        window.location.href = newUrl + this.buildQueryString();
    }

    onFilterChange() {
        const url = this.buildFilterUrl();

        // Prostě přesměrovat na novou URL
        window.location.href = url;
    }

    getSelectedCategory() {
        const selectedRadio = this.categoryRadioTargets.find(radio => radio.checked);
        return selectedRadio ? selectedRadio.value : null;
    }

    clearBreedFilters() {
        this.breedCheckboxTargets.forEach(checkbox => {
            checkbox.checked = false;
        });
    }

    updateBreedOptions() {
        const selectedCategory = this.getSelectedCategory();

        this.breedCheckboxTargets.forEach(checkbox => {
            const breedCategory = checkbox.dataset.category;
            const filterOption = checkbox.closest('.filter-option');

            if (selectedCategory === 'all' || !selectedCategory) {
                // Zobrazit všechna plemena
                filterOption.style.display = 'block';
            } else {
                // Zobrazit pouze plemena vybrané kategorie
                if (breedCategory === selectedCategory) {
                    filterOption.style.display = 'block';
                } else {
                    filterOption.style.display = 'none';
                    // Odznačit skryté checkboxy pouze pokud nejsou disabled
                    if (!checkbox.disabled) {
                        checkbox.checked = false;
                    }
                }
            }
        });
    }

    buildFilterUrl() {
        let url = this.baseUrlValue;

        // Přidat kategorii do URL
        const selectedCategory = this.getSelectedCategory();
        if (selectedCategory && selectedCategory !== 'all') {
            url += '/' + selectedCategory;
        }

        url += this.buildQueryString();
        return url;
    }

    buildQueryString() {
        const params = new URLSearchParams();

        // Plemena
        this.breedCheckboxTargets.forEach(checkbox => {
            if (checkbox.checked && !checkbox.disabled && checkbox.closest('.filter-option').style.display !== 'none') {
                params.append('breeds[]', checkbox.value);
            }
        });

        // Kraje
        this.regionCheckboxTargets.forEach(checkbox => {
            if (checkbox.checked && !checkbox.disabled) {
                params.append('regions[]', checkbox.value);
            }
        });

        // Typy inzerátů
        this.typeCheckboxTargets.forEach(checkbox => {
            if (checkbox.checked && !checkbox.disabled) {
                params.append('types[]', checkbox.value);
            }
        });

        // Ceny
        const priceFrom = this.element.querySelector('input[name="priceFrom"]')?.value;
        const priceTo = this.element.querySelector('input[name="priceTo"]')?.value;

        if (priceFrom) {
            params.append('priceFrom', priceFrom);
        }

        if (priceTo) {
            params.append('priceTo', priceTo);
        }

        // Vyhledávání
        const search = this.element.querySelector('input[name="search"]')?.value;
        if (search) {
            params.append('search', search);
        }

        const queryString = params.toString();
        return queryString ? '?' + queryString : '';
    }



    clearAllFilters() {
        // Vyčistit všechny checkboxy
        [...this.breedCheckboxTargets, ...this.regionCheckboxTargets, ...this.typeCheckboxTargets].forEach(checkbox => {
            checkbox.checked = false;
        });

        // Vyčistit cenová pole
        const priceInputs = this.element.querySelectorAll('input[name="priceFrom"], input[name="priceTo"]');
        priceInputs.forEach(input => {
            input.value = '';
        });

        // Vyčistit vyhledávací pole
        const searchInput = this.element.querySelector('input[name="search"]');
        if (searchInput) {
            searchInput.value = '';
        }

        // Nastavit kategorii na "Všechny"
        const allCategoryRadio = this.categoryRadioTargets.find(radio => radio.value === 'all');
        if (allCategoryRadio) {
            allCategoryRadio.checked = true;
        }

        // Aktualizovat filtry
        this.onCategoryChange();
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}
