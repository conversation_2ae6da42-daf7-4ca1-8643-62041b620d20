@import "tailwindcss";

/* Custom styles for filter sidebar */
.filter-option {
    transition: all 0.2s ease-in-out;
}

.filter-option.hidden {
    display: none !important;
}

/* Disabled filter options */
input[type="checkbox"]:disabled,
input[type="radio"]:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

input[type="checkbox"]:disabled + span,
input[type="radio"]:disabled + span {
    color: #9ca3af;
    cursor: not-allowed;
}

/* Filter count badges */
.filter-count {
    font-size: 0.75rem;
    font-weight: 500;
    min-width: 1.5rem;
    text-align: center;
}

.filter-count.zero {
    background-color: #f3f4f6;
    color: #9ca3af;
}

.filter-count.active {
    background-color: #e0e7ff;
    color: #4338ca;
}

/* Smooth scrolling for filter sections */
.filter-scroll {
    scrollbar-width: thin;
    scrollbar-color: #e5e7eb #f9fafb;
}

.filter-scroll::-webkit-scrollbar {
    width: 6px;
}

.filter-scroll::-webkit-scrollbar-track {
    background: #f9fafb;
    border-radius: 3px;
}

.filter-scroll::-webkit-scrollbar-thumb {
    background: #e5e7eb;
    border-radius: 3px;
}

.filter-scroll::-webkit-scrollbar-thumb:hover {
    background: #d1d5db;
}

/* Loading state */
.loading {
    opacity: 0.5;
    pointer-events: none;
    transition: opacity 0.3s ease;
}

/* Responsive grid adjustments */
@media (min-width: 1024px) {
    .posts-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1280px) {
    .posts-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}
