<?php

namespace App\Controller;

use App\Entity\Post;
use App\Entity\Category;
use App\Repository\PostRepository;
use App\Service\PostFilterService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

class PostController extends AbstractController
{
    public function __construct(
        private PostRepository $postRepository,
        private PostFilterService $filterService,
        private EntityManagerInterface $entityManager
    ) {
    }

    #[Route('/inzeraty', name: 'app_posts')]
    public function index(Request $request): Response
    {
        return $this->handlePostsListing($request);
    }

    #[Route('/inzeraty/{categorySlug}', name: 'app_posts_category')]
    public function category(string $categorySlug, Request $request): Response
    {
        $category = $this->entityManager->getRepository(Category::class)->findOneBy([
            'slug' => $categorySlug
        ]);

        if (!$category) {
            throw $this->createNotFoundException('Kategorie nebyla nalezena.');
        }

        return $this->handlePostsListing($request, $category);
    }

    private function handlePostsListing(Request $request, ?Category $category = null): Response
    {
        // Získání filtrů z requestu
        $filters = $this->filterService->getFiltersFromRequest($request, $category);

        // Načtení inzerátů s filtry
        $posts = $this->postRepository->findWithFilters($filters);

        // Počet inzerátů
        $totalCount = $this->postRepository->countWithFilters($filters);

        // Data pro filtry s počty
        $filterData = $this->filterService->getFilterData($category, $filters);

        // Počet aktivních filtrů
        $activeFiltersCount = $this->filterService->getActiveFiltersCount($filters);

        return $this->render('post/index.html.twig', [
            'posts' => $posts,
            'totalCount' => $totalCount,
            'currentCategory' => $category,
            'filterData' => $filterData,
            'currentFilters' => $filters,
            'activeFiltersCount' => $activeFiltersCount,
            'request' => $request
        ]);
    }

    #[Route('/inzerat/{slug}', name: 'app_post_detail')]
    public function detail(string $slug): Response
    {
        $post = $this->postRepository->findOneBy([
            'slug' => $slug,
            'isActive' => true
        ]);

        if (!$post) {
            throw $this->createNotFoundException('Inzerát nebyl nalezen.');
        }

        return $this->render('post/detail.html.twig', [
            'post' => $post,
        ]);
    }
}
