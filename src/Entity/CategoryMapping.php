<?php

namespace App\Entity;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
#[ORM\Table(name: 'category_mappings')]
#[ORM\HasLifecycleCallbacks]
class CategoryMapping
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: Types::INTEGER)]
    private ?int $id = null;

    #[ORM\Column(type: Types::STRING, length: 100)]
    private string $sourceSite;

    #[ORM\Column(type: Types::STRING, length: 150)]
    private string $sourceCategory;

    #[ORM\ManyToOne(targetEntity: Category::class, inversedBy: 'categoryMappings')]
    #[ORM\JoinColumn(nullable: false)]
    private Category $targetCategory;

    #[ORM\Column(type: Types::DATETIME_IMMUTABLE)]
    private \DateTimeImmutable $createdAt;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private \DateTime $updatedAt;

    public function __construct()
    {
        $this->createdAt = new \DateTimeImmutable();
        $this->updatedAt = new \DateTime();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getSourceSite(): string
    {
        return $this->sourceSite;
    }

    public function setSourceSite(string $sourceSite): static
    {
        $this->sourceSite = $sourceSite;
        return $this;
    }

    public function getSourceCategory(): string
    {
        return $this->sourceCategory;
    }

    public function setSourceCategory(string $sourceCategory): static
    {
        $this->sourceCategory = $sourceCategory;
        return $this;
    }

    public function getTargetCategory(): Category
    {
        return $this->targetCategory;
    }

    public function setTargetCategory(?Category $targetCategory): static
    {
        $this->targetCategory = $targetCategory;
        return $this;
    }

    public function getCreatedAt(): \DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeImmutable $createdAt): static
    {
        $this->createdAt = $createdAt;
        return $this;
    }

    public function getUpdatedAt(): \DateTime
    {
        return $this->updatedAt;
    }

    public function setUpdatedAt(\DateTime $updatedAt): static
    {
        $this->updatedAt = $updatedAt;
        return $this;
    }

    #[ORM\PreUpdate]
    public function preUpdate(): void
    {
        $this->updatedAt = new \DateTime();
    }

    public function __toString(): string
    {
        return sprintf('%s: %s -> %s', $this->sourceSite, $this->sourceCategory, $this->targetCategory->getName());
    }
}
