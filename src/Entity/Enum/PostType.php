<?php

namespace App\Entity\Enum;

enum PostType: string
{
    case SELL = 'prodám';
    case BUY = 'koupím';
    case GIVE = 'daruji';

    public function getLabel(): string
    {
        return match($this) {
            self::SELL => 'Prodám',
            self::BUY => 'Koupím',
            self::GIVE => 'Daruji',
        };
    }

    public function getDescription(): string
    {
        return match($this) {
            self::SELL => 'Prodej zvířete',
            self::BUY => 'Hledání zvířete ke koupi',
            self::GIVE => 'Darování zvířete zdarma',
        };
    }
}
