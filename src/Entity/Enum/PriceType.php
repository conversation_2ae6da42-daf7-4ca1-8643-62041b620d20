<?php

namespace App\Entity\Enum;

enum PriceType: string
{
    case CZK = 'CZK';
    case EUR = 'EUR';
    case AGREEMENT = 'Dohodou';
    case IN_TEXT = 'V textu';

    public function getLabel(): string
    {
        return match($this) {
            self::CZK => 'Cena v korunách',
            self::EUR => 'Cena v eurech',
            self::AGREEMENT => 'Cena dohodou',
            self::IN_TEXT => 'Cena uvedena v popisu',
        };
    }

    public function getSymbol(): ?string
    {
        return match($this) {
            self::CZK => 'Kč',
            self::EUR => '€',
            self::AGREEMENT => null,
            self::IN_TEXT => null,
        };
    }

    public function hasCurrency(): bool
    {
        return in_array($this, [self::CZK, self::EUR]);
    }
}
