<?php

namespace App\Entity\Enum;

enum Country: string
{
    case CZ = 'ČR';
    case SK = 'SR';
    case EU = 'EU';

    public function getLabel(): string
    {
        return match($this) {
            self::CZ => 'Česká republika',
            self::SK => 'Slovenská republika',
            self::EU => 'Ostatní EU',
        };
    }

    public function getCode(): string
    {
        return match($this) {
            self::CZ => 'CZ',
            self::SK => 'SK',
            self::EU => 'EU',
        };
    }
}
