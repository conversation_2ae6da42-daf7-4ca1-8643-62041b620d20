<?php

namespace App\DataFixtures;

use App\Entity\Category;
use App\Entity\Region;
use App\Entity\Breed;
use App\Entity\User;
use App\Entity\Post;
use App\Entity\PostImage;
use App\Entity\Enum\PostType;
use App\Entity\Enum\Country;
use App\Entity\Enum\PriceType;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Persistence\ObjectManager;

class AppFixtures extends Fixture
{
    public function load(ObjectManager $manager): void
    {
        // Vytvoření kategorií
        $categoryDogs = new Category();
        $categoryDogs->setName('Psi');
        $categoryDogs->setSlug('psi');
        $manager->persist($categoryDogs);

        $categoryCats = new Category();
        $categoryCats->setName('Kočky');
        $categoryCats->setSlug('kocky');
        $manager->persist($categoryCats);

        $categoryBirds = new Category();
        $categoryBirds->setName('Ptáci');
        $categoryBirds->setSlug('ptaci');
        $manager->persist($categoryBirds);

        // Vytvoření krajů
        $regionPrague = new Region();
        $regionPrague->setName('Hlavní město Praha');
        $regionPrague->setSlug('hlavni-mesto-praha');
        $manager->persist($regionPrague);

        $regionSouthBohemia = new Region();
        $regionSouthBohemia->setName('Jihočeský kraj');
        $regionSouthBohemia->setSlug('jihocesky-kraj');
        $manager->persist($regionSouthBohemia);

        $regionSouthMoravia = new Region();
        $regionSouthMoravia->setName('Jihomoravský kraj');
        $regionSouthMoravia->setSlug('jihomoravsky-kraj');
        $manager->persist($regionSouthMoravia);

        $regionCentralBohemia = new Region();
        $regionCentralBohemia->setName('Středočeský kraj');
        $regionCentralBohemia->setSlug('stredocesky-kraj');
        $manager->persist($regionCentralBohemia);

        $regionMoravianSilesia = new Region();
        $regionMoravianSilesia->setName('Moravskoslezský kraj');
        $regionMoravianSilesia->setSlug('moravskoslezsky-kraj');
        $manager->persist($regionMoravianSilesia);

        // Vytvoření plemen
        $breedLabrador = new Breed();
        $breedLabrador->setName('Labradorský retrívr');
        $breedLabrador->setSlug('labradorsky-retrivr');
        $breedLabrador->setCategory($categoryDogs);
        $manager->persist($breedLabrador);

        $breedGermanShepherd = new Breed();
        $breedGermanShepherd->setName('Německý ovčák');
        $breedGermanShepherd->setSlug('nemecky-ovcak');
        $breedGermanShepherd->setCategory($categoryDogs);
        $manager->persist($breedGermanShepherd);

        $breedPersian = new Breed();
        $breedPersian->setName('Perská kočka');
        $breedPersian->setSlug('perska-kocka');
        $breedPersian->setCategory($categoryCats);
        $manager->persist($breedPersian);

        $breedCanary = new Breed();
        $breedCanary->setName('Kanárek');
        $breedCanary->setSlug('kanarek');
        $breedCanary->setCategory($categoryBirds);
        $manager->persist($breedCanary);

        // Další plemena psů
        $breedGoldenRetriever = new Breed();
        $breedGoldenRetriever->setName('Golden retrívr');
        $breedGoldenRetriever->setSlug('golden-retrivr');
        $breedGoldenRetriever->setCategory($categoryDogs);
        $manager->persist($breedGoldenRetriever);

        $breedBulldog = new Breed();
        $breedBulldog->setName('Francouzský buldoček');
        $breedBulldog->setSlug('francouzsky-buldocek');
        $breedBulldog->setCategory($categoryDogs);
        $manager->persist($breedBulldog);

        // Další plemena koček
        $breedBritish = new Breed();
        $breedBritish->setName('Britská krátkosrstá');
        $breedBritish->setSlug('britska-kratkosrsta');
        $breedBritish->setCategory($categoryCats);
        $manager->persist($breedBritish);

        $breedMaineCoon = new Breed();
        $breedMaineCoon->setName('Maine Coon');
        $breedMaineCoon->setSlug('maine-coon');
        $breedMaineCoon->setCategory($categoryCats);
        $manager->persist($breedMaineCoon);

        // Další ptáci
        $breedParrot = new Breed();
        $breedParrot->setName('Papoušek');
        $breedParrot->setSlug('papousek');
        $breedParrot->setCategory($categoryBirds);
        $manager->persist($breedParrot);

        // Vytvoření uživatelů
        $user1 = new User();
        $user1->setPhoneNumber('+420 123 456 789');
        $manager->persist($user1);

        $user2 = new User();
        $user2->setPhoneNumber('+420 987 654 321');
        $manager->persist($user2);

        $user3 = new User();
        $user3->setPhoneNumber('+420 777 888 999');
        $manager->persist($user3);

        $user4 = new User();
        $user4->setPhoneNumber('+420 111 222 333');
        $manager->persist($user4);

        // Flush pro získání ID
        $manager->flush();

        // Vytvoření příspěvků
        $post1 = new Post();
        $post1->setTitle('Prodám štěně Labradora');
        $post1->setPostType(PostType::SELL);
        $post1->setCountry(Country::CZ);
        $post1->setRegion($regionPrague);
        $post1->setCategory($categoryDogs);
        $post1->setBreed($breedLabrador);
        $post1->setPriceType(PriceType::CZK);
        $post1->setPriceAmount('15000');
        $post1->setDescription('Krásné štěně labradorského retrívra, očkované, odčervené. Rodiče s PP.');
        $post1->setContactPhone('+420 123 456 789');
        $post1->setUser($user1);
        $post1->setSlug('prodam-stene-labradora-' . time());
        $manager->persist($post1);

        $post2 = new Post();
        $post2->setTitle('Hledám perskou kočku');
        $post2->setPostType(PostType::BUY);
        $post2->setCountry(Country::CZ);
        $post2->setRegion($regionSouthBohemia);
        $post2->setCategory($categoryCats);
        $post2->setBreed($breedPersian);
        $post2->setPriceType(PriceType::AGREEMENT);
        $post2->setDescription('Hledám mladou perskou kočku, nejlépe samičku. Cena dohodou.');
        $post2->setContactPhone('+420 987 654 321');
        $post2->setUser($user2);
        $post2->setSlug('hledam-perskou-kocku-' . time());
        $manager->persist($post2);

        $post3 = new Post();
        $post3->setTitle('Daruji kanárky');
        $post3->setPostType(PostType::GIVE);
        $post3->setCountry(Country::CZ);
        $post3->setRegion($regionSouthMoravia);
        $post3->setCategory($categoryBirds);
        $post3->setBreed($breedCanary);
        $post3->setDescription('Darujem mladé kanárky do dobrých rúk. Sú zdravé a krásne spievajú.');
        $post3->setContactPhone('+420 123 456 789');
        $post3->setUser($user1);
        $post3->setSlug('daruji-kanarky-' . time());
        $manager->persist($post3);

        // Více psů
        $post4 = new Post();
        $post4->setTitle('Prodám štěně Golden Retrívra');
        $post4->setPostType(PostType::SELL);
        $post4->setCountry(Country::CZ);
        $post4->setRegion($regionCentralBohemia);
        $post4->setCategory($categoryDogs);
        $post4->setBreed($breedGoldenRetriever);
        $post4->setPriceType(PriceType::CZK);
        $post4->setPriceAmount('18000');
        $post4->setDescription('Krásné štěně Golden Retrívra, 8 týdnů staré. Očkované, odčervené, s PP. Rodiče jsou výstavní psi s výbornými výsledky.');
        $post4->setContactPhone('+420 777 888 999');
        $post4->setUser($user3);
        $post4->setSlug('prodam-stene-golden-retrivra-' . (time() + 1));
        $manager->persist($post4);

        $post5 = new Post();
        $post5->setTitle('Hledám Německého ovčáka');
        $post5->setPostType(PostType::BUY);
        $post5->setCountry(Country::CZ);
        $post5->setRegion($regionPrague);
        $post5->setCategory($categoryDogs);
        $post5->setBreed($breedGermanShepherd);
        $post5->setPriceType(PriceType::AGREEMENT);
        $post5->setDescription('Hledám mladého Německého ovčáka, nejlépe samce. Preferuji psy s PP. Cena dohodou podle kvality.');
        $post5->setContactPhone('+420 111 222 333');
        $post5->setUser($user4);
        $post5->setSlug('hledam-nemeckeho-ovcaka-' . (time() + 2));
        $manager->persist($post5);

        $post6 = new Post();
        $post6->setTitle('Prodám Francouzského buldočka');
        $post6->setPostType(PostType::SELL);
        $post6->setCountry(Country::CZ);
        $post6->setRegion($regionMoravianSilesia);
        $post6->setCategory($categoryDogs);
        $post6->setBreed($breedBulldog);
        $post6->setPriceType(PriceType::CZK);
        $post6->setPriceAmount('25000');
        $post6->setDescription('Prodám krásného Francouzského buldočka, 3 měsíce starého. Má všechna očkování a je zdravý.');
        $post6->setContactPhone('+420 987 654 321');
        $post6->setUser($user2);
        $post6->setSlug('prodam-francouzskeho-buldocka-' . (time() + 3));
        $manager->persist($post6);

        // Více koček
        $post7 = new Post();
        $post7->setTitle('Prodám koťata Britské krátkosrsté');
        $post7->setPostType(PostType::SELL);
        $post7->setCountry(Country::CZ);
        $post7->setRegion($regionSouthBohemia);
        $post7->setCategory($categoryCats);
        $post7->setBreed($breedBritish);
        $post7->setPriceType(PriceType::CZK);
        $post7->setPriceAmount('8000');
        $post7->setDescription('Prodám krásná koťata Britské krátkosrsté kočky. Modrá a stříbrná barva. Koťata jsou zdravá a socializovaná.');
        $post7->setContactPhone('+420 123 456 789');
        $post7->setUser($user1);
        $post7->setSlug('prodam-kotata-britske-kratkosrste-' . (time() + 4));
        $manager->persist($post7);

        $post8 = new Post();
        $post8->setTitle('Daruji Maine Coon kočku');
        $post8->setPostType(PostType::GIVE);
        $post8->setCountry(Country::CZ);
        $post8->setRegion($regionPrague);
        $post8->setCategory($categoryCats);
        $post8->setBreed($breedMaineCoon);
        $post8->setDescription('Daruji krásnou Maine Coon kočku do dobrých rukou. Je to 2 roky stará samička, kastrovaná a zdravá.');
        $post8->setContactPhone('+420 777 888 999');
        $post8->setUser($user3);
        $post8->setSlug('daruji-maine-coon-kocku-' . (time() + 5));
        $manager->persist($post8);

        $post9 = new Post();
        $post9->setTitle('Koupím Perskou kočku - samičku');
        $post9->setPostType(PostType::BUY);
        $post9->setCountry(Country::CZ);
        $post9->setRegion($regionCentralBohemia);
        $post9->setCategory($categoryCats);
        $post9->setBreed($breedPersian);
        $post9->setPriceType(PriceType::CZK);
        $post9->setPriceAmount('12000');
        $post9->setDescription('Hledám mladou Perskou kočku, samičku. Preferuji bílou nebo krémovou barvu. Nabízím až 12 000 Kč.');
        $post9->setContactPhone('+420 111 222 333');
        $post9->setUser($user4);
        $post9->setSlug('koupim-perskou-kocku-samicku-' . (time() + 6));
        $manager->persist($post9);

        // Více ptáků
        $post10 = new Post();
        $post10->setTitle('Prodám mluvícího papouška');
        $post10->setPostType(PostType::SELL);
        $post10->setCountry(Country::CZ);
        $post10->setRegion($regionMoravianSilesia);
        $post10->setCategory($categoryBirds);
        $post10->setBreed($breedParrot);
        $post10->setPriceType(PriceType::CZK);
        $post10->setPriceAmount('15000');
        $post10->setDescription('Prodám krásného mluvícího papouška. Umí říkat několik slov a je velmi přátelský. Včetně klece a příslušenství.');
        $post10->setContactPhone('+420 987 654 321');
        $post10->setUser($user2);
        $post10->setSlug('prodam-mluviciho-papouska-' . (time() + 7));
        $manager->persist($post10);

        $post11 = new Post();
        $post11->setTitle('Hledám kanárky pro chov');
        $post11->setPostType(PostType::BUY);
        $post11->setCountry(Country::CZ);
        $post11->setRegion($regionSouthMoravia);
        $post11->setCategory($categoryBirds);
        $post11->setBreed($breedCanary);
        $post11->setPriceType(PriceType::AGREEMENT);
        $post11->setDescription('Hledám kvalitní kanárky pro chov. Preferuji zpěvné kanárky. Cena dohodou podle kvality.');
        $post11->setContactPhone('+420 123 456 789');
        $post11->setUser($user1);
        $post11->setSlug('hledam-kanarky-pro-chov-' . (time() + 8));
        $manager->persist($post11);

        // Další psi s různými cenami
        $post12 = new Post();
        $post12->setTitle('Levně prodám štěně Labradora');
        $post12->setPostType(PostType::SELL);
        $post12->setCountry(Country::CZ);
        $post12->setRegion($regionSouthBohemia);
        $post12->setCategory($categoryDogs);
        $post12->setBreed($breedLabrador);
        $post12->setPriceType(PriceType::CZK);
        $post12->setPriceAmount('8000');
        $post12->setDescription('Prodám štěně Labradora za výhodnou cenu. Štěně je zdravé, ale bez PP. Ideální jako rodinný pes.');
        $post12->setContactPhone('+420 777 888 999');
        $post12->setUser($user3);
        $post12->setSlug('levne-prodam-stene-labradora-' . (time() + 9));
        $manager->persist($post12);

        $post13 = new Post();
        $post13->setTitle('Daruji staršího psa');
        $post13->setPostType(PostType::GIVE);
        $post13->setCountry(Country::CZ);
        $post13->setRegion($regionPrague);
        $post13->setCategory($categoryDogs);
        $post13->setDescription('Daruji 8 let starého psa do dobrých rukou. Je to kříženec, velmi hodný a poslušný. Kvůli stěhování.');
        $post13->setContactPhone('+420 111 222 333');
        $post13->setUser($user4);
        $post13->setSlug('daruji-starsiho-psa-' . (time() + 10));
        $manager->persist($post13);

        // Vytvoření obrázků pro příspěvky
        $image1 = new PostImage();
        $image1->setPost($post1);
        $image1->setImageUrl('https://example.com/labrador1.jpg');
        $image1->setOrder(0); // hlavní obrázek
        $manager->persist($image1);

        $image2 = new PostImage();
        $image2->setPost($post1);
        $image2->setImageUrl('https://example.com/labrador2.jpg');
        $image2->setOrder(1);
        $manager->persist($image2);

        $image3 = new PostImage();
        $image3->setPost($post3);
        $image3->setImageUrl('https://example.com/canary1.jpg');
        $image3->setOrder(0);
        $manager->persist($image3);

        // Obrázky pro Golden Retrívra
        $image4 = new PostImage();
        $image4->setPost($post4);
        $image4->setImageUrl('https://example.com/golden1.jpg');
        $image4->setOrder(0);
        $manager->persist($image4);

        $image5 = new PostImage();
        $image5->setPost($post4);
        $image5->setImageUrl('https://example.com/golden2.jpg');
        $image5->setOrder(1);
        $manager->persist($image5);

        // Obrázky pro Francouzského buldočka
        $image6 = new PostImage();
        $image6->setPost($post6);
        $image6->setImageUrl('https://example.com/bulldog1.jpg');
        $image6->setOrder(0);
        $manager->persist($image6);

        // Obrázky pro Britskou kočku
        $image7 = new PostImage();
        $image7->setPost($post7);
        $image7->setImageUrl('https://example.com/british1.jpg');
        $image7->setOrder(0);
        $manager->persist($image7);

        $image8 = new PostImage();
        $image8->setPost($post7);
        $image8->setImageUrl('https://example.com/british2.jpg');
        $image8->setOrder(1);
        $manager->persist($image8);

        $image9 = new PostImage();
        $image9->setPost($post7);
        $image9->setImageUrl('https://example.com/british3.jpg');
        $image9->setOrder(2);
        $manager->persist($image9);

        // Obrázky pro Maine Coon
        $image10 = new PostImage();
        $image10->setPost($post8);
        $image10->setImageUrl('https://example.com/mainecoon1.jpg');
        $image10->setOrder(0);
        $manager->persist($image10);

        // Obrázky pro papouška
        $image11 = new PostImage();
        $image11->setPost($post10);
        $image11->setImageUrl('https://example.com/parrot1.jpg');
        $image11->setOrder(0);
        $manager->persist($image11);

        $image12 = new PostImage();
        $image12->setPost($post10);
        $image12->setImageUrl('https://example.com/parrot2.jpg');
        $image12->setOrder(1);
        $manager->persist($image12);

        $manager->flush();
    }
}
