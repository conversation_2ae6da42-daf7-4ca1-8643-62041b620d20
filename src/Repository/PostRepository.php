<?php

namespace App\Repository;

use App\Entity\Post;
use App\Entity\Category;
use App\Entity\Breed;
use App\Entity\Region;
use App\Entity\Enum\PostType;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Doctrine\ORM\QueryBuilder;

/**
 * @extends ServiceEntityRepository<Post>
 */
class PostRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Post::class);
    }

    public function findWithFilters(array $filters = []): array
    {
        $qb = $this->createQueryBuilder('p')
            ->leftJoin('p.category', 'c')
            ->leftJoin('p.breed', 'b')
            ->leftJoin('p.region', 'r')
            ->leftJoin('p.user', 'u')
            ->where('p.isActive = :isActive')
            ->setParameter('isActive', true)
            ->orderBy('p.createdAt', 'DESC');

        $this->applyFilters($qb, $filters);

        return $qb->getQuery()->getResult();
    }

    public function countWithFilters(array $filters = []): int
    {
        $qb = $this->createQueryBuilder('p')
            ->select('COUNT(p.id)')
            ->leftJoin('p.category', 'c')
            ->leftJoin('p.breed', 'b')
            ->leftJoin('p.region', 'r')
            ->where('p.isActive = :isActive')
            ->setParameter('isActive', true);

        $this->applyFilters($qb, $filters);

        return (int) $qb->getQuery()->getSingleScalarResult();
    }

    private function applyFilters(QueryBuilder $qb, array $filters): void
    {
        // Filtr podle kategorie
        if (!empty($filters['category'])) {
            if ($filters['category'] instanceof Category) {
                $qb->andWhere('p.category = :category')
                   ->setParameter('category', $filters['category']);
            } elseif (is_string($filters['category'])) {
                $qb->andWhere('c.slug = :categorySlug')
                   ->setParameter('categorySlug', $filters['category']);
            }
        }

        // Filtr podle plemen (array)
        if (!empty($filters['breeds']) && is_array($filters['breeds'])) {
            $qb->andWhere('p.breed IN (:breeds)')
               ->setParameter('breeds', $filters['breeds']);
        }

        // Filtr podle krajů (array)
        if (!empty($filters['regions']) && is_array($filters['regions'])) {
            $qb->andWhere('p.region IN (:regions)')
               ->setParameter('regions', $filters['regions']);
        }

        // Filtr podle typů inzerátů (array)
        if (!empty($filters['types']) && is_array($filters['types'])) {
            $qb->andWhere('p.postType IN (:types)')
               ->setParameter('types', $filters['types']);
        }


    }

    public function findByCategorySlug(string $categorySlug): array
    {
        return $this->createQueryBuilder('p')
            ->leftJoin('p.category', 'c')
            ->where('p.isActive = :isActive')
            ->andWhere('c.slug = :categorySlug')
            ->setParameter('isActive', true)
            ->setParameter('categorySlug', $categorySlug)
            ->orderBy('p.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    public function getAvailableBreedsByCategory(?Category $category = null): array
    {
        $qb = $this->getEntityManager()->createQueryBuilder()
            ->select('DISTINCT b')
            ->from(Breed::class, 'b')
            ->leftJoin('b.posts', 'p')
            ->where('p.isActive = :isActive')
            ->setParameter('isActive', true)
            ->orderBy('b.name', 'ASC');

        if ($category) {
            $qb->andWhere('b.category = :category')
               ->setParameter('category', $category);
        }

        return $qb->getQuery()->getResult();
    }

    public function getBreedsWithCounts(?Category $category = null, array $currentFilters = []): array
    {
        $qb = $this->createQueryBuilder('p')
            ->select('b.id, b.name, b.slug, COUNT(p.id) as postCount')
            ->leftJoin('p.breed', 'b')
            ->leftJoin('p.category', 'c')
            ->leftJoin('p.region', 'r')
            ->where('p.isActive = :isActive')
            ->andWhere('b.id IS NOT NULL')
            ->setParameter('isActive', true)
            ->groupBy('b.id, b.name, b.slug')
            ->orderBy('b.name', 'ASC');

        if ($category) {
            $qb->andWhere('b.category = :category')
               ->setParameter('category', $category);
        }

        // Pro facetové filtry: aplikovat pouze hierarchicky vyšší filtry
        $this->applyFacetFilters($qb, $currentFilters, 'breeds');

        return $qb->getQuery()->getResult();
    }

    public function getAvailableRegions(): array
    {
        return $this->getEntityManager()->createQueryBuilder()
            ->select('DISTINCT r')
            ->from(Region::class, 'r')
            ->leftJoin('r.posts', 'p')
            ->where('p.isActive = :isActive')
            ->setParameter('isActive', true)
            ->orderBy('r.name', 'ASC')
            ->getQuery()
            ->getResult();
    }

    public function getRegionsWithCounts(array $currentFilters = []): array
    {
        $qb = $this->createQueryBuilder('p')
            ->select('r.id, r.name, r.slug, COUNT(p.id) as postCount')
            ->leftJoin('p.region', 'r')
            ->leftJoin('p.category', 'c')
            ->leftJoin('p.breed', 'b')
            ->where('p.isActive = :isActive')
            ->andWhere('r.id IS NOT NULL')
            ->setParameter('isActive', true)
            ->groupBy('r.id, r.name, r.slug')
            ->orderBy('r.name', 'ASC');

        // Pro facetové filtry: aplikovat pouze hierarchicky vyšší filtry
        $this->applyFacetFilters($qb, $currentFilters, 'regions');

        return $qb->getQuery()->getResult();
    }

    public function getAvailablePostTypes(): array
    {
        $result = $this->createQueryBuilder('p')
            ->select('DISTINCT p.postType')
            ->where('p.isActive = :isActive')
            ->setParameter('isActive', true)
            ->getQuery()
            ->getResult();

        return array_map(fn($item) => $item['postType'], $result);
    }

    public function getPostTypesWithCounts(array $currentFilters = []): array
    {
        $qb = $this->createQueryBuilder('p')
            ->select('p.postType, COUNT(p.id) as postCount')
            ->leftJoin('p.category', 'c')
            ->leftJoin('p.breed', 'b')
            ->leftJoin('p.region', 'r')
            ->where('p.isActive = :isActive')
            ->setParameter('isActive', true)
            ->groupBy('p.postType');

        // Pro facetové filtry: aplikovat pouze hierarchicky vyšší filtry
        $this->applyFacetFilters($qb, $currentFilters, 'types');

        $result = $qb->getQuery()->getResult();

        return array_map(function($item) {
            return [
                'type' => $item['postType'],
                'count' => (int) $item['postCount']
            ];
        }, $result);
    }



    private function applyFiltersForCounting(QueryBuilder $qb, array $filters, array $excludeFilters = []): void
    {
        // Přidat potřebné joiny pokud ještě nejsou
        $this->ensureJoinsForCounting($qb, $filters, $excludeFilters);

        // Filtr podle kategorie
        if (!in_array('category', $excludeFilters) && !empty($filters['category'])) {
            if ($filters['category'] instanceof Category) {
                $qb->andWhere('p.category = :category')
                   ->setParameter('category', $filters['category']);
            } elseif (is_string($filters['category'])) {
                $qb->andWhere('c.slug = :categorySlug')
                   ->setParameter('categorySlug', $filters['category']);
            }
        }

        // Filtr podle plemen (array)
        if (!in_array('breeds', $excludeFilters) && !empty($filters['breeds']) && is_array($filters['breeds'])) {
            $qb->andWhere('p.breed IN (:breeds)')
               ->setParameter('breeds', $filters['breeds']);
        }

        // Filtr podle krajů (array)
        if (!in_array('regions', $excludeFilters) && !empty($filters['regions']) && is_array($filters['regions'])) {
            $qb->andWhere('p.region IN (:regions)')
               ->setParameter('regions', $filters['regions']);
        }

        // Filtr podle typů inzerátů (array)
        if (!in_array('types', $excludeFilters) && !empty($filters['types']) && is_array($filters['types'])) {
            $qb->andWhere('p.postType IN (:types)')
               ->setParameter('types', $filters['types']);
        }

        // Filtr podle ceny (rozsah)
        if (!in_array('price', $excludeFilters)) {
            if (!empty($filters['priceFrom'])) {
                $qb->andWhere('p.priceAmount >= :priceFrom')
                   ->setParameter('priceFrom', $filters['priceFrom']);
            }

            if (!empty($filters['priceTo'])) {
                $qb->andWhere('p.priceAmount <= :priceTo')
                   ->setParameter('priceTo', $filters['priceTo']);
            }
        }

        // Textové vyhledávání
        if (!in_array('search', $excludeFilters) && !empty($filters['search'])) {
            $qb->andWhere('(p.title LIKE :search OR p.description LIKE :search)')
               ->setParameter('search', '%' . $filters['search'] . '%');
        }
    }

    /**
     * Aplikuje facetové filtry - pouze hierarchicky vyšší filtry
     *
     * Hierarchie filtrů:
     * 1. Kategorie (nejvyšší)
     * 2. Plemena (závislé na kategorii)
     * 3. Kraje, Typy, Cena, Vyhledávání (nezávislé na sobě, ale závislé na kategorii a plemenech)
     */
    private function applyFacetFilters(QueryBuilder $qb, array $filters, string $currentFilterType): void
    {
        switch ($currentFilterType) {
            case 'category':
                // Pro kategorie: aplikovat všechny filtry KROMĚ kategorie a plemen
                if (!empty($filters['regions']) && is_array($filters['regions'])) {
                    $qb->andWhere('p.region IN (:regions)')
                       ->setParameter('regions', $filters['regions']);
                }
                if (!empty($filters['types']) && is_array($filters['types'])) {
                    $qb->andWhere('p.postType IN (:types)')
                       ->setParameter('types', $filters['types']);
                }
                break;

            case 'breeds':
                // Pro plemena: aplikovat pouze kategorii
                if (!empty($filters['category'])) {
                    if ($filters['category'] instanceof Category) {
                        $qb->andWhere('p.category = :category')
                           ->setParameter('category', $filters['category']);
                    } elseif (is_string($filters['category'])) {
                        $qb->andWhere('c.slug = :categorySlug')
                           ->setParameter('categorySlug', $filters['category']);
                    }
                }
                break;

            case 'regions':
                // Pro kraje: aplikovat kategorii a plemena
                if (!empty($filters['category'])) {
                    if ($filters['category'] instanceof Category) {
                        $qb->andWhere('p.category = :category')
                           ->setParameter('category', $filters['category']);
                    } elseif (is_string($filters['category'])) {
                        $qb->andWhere('c.slug = :categorySlug')
                           ->setParameter('categorySlug', $filters['category']);
                    }
                }

                if (!empty($filters['breeds']) && is_array($filters['breeds'])) {
                    $qb->andWhere('p.breed IN (:breeds)')
                       ->setParameter('breeds', $filters['breeds']);
                }
                break;

            case 'types':
                // Pro typy: aplikovat kategorii a plemena
                if (!empty($filters['category'])) {
                    if ($filters['category'] instanceof Category) {
                        $qb->andWhere('p.category = :category')
                           ->setParameter('category', $filters['category']);
                    } elseif (is_string($filters['category'])) {
                        $qb->andWhere('c.slug = :categorySlug')
                           ->setParameter('categorySlug', $filters['category']);
                    }
                }

                if (!empty($filters['breeds']) && is_array($filters['breeds'])) {
                    $qb->andWhere('p.breed IN (:breeds)')
                       ->setParameter('breeds', $filters['breeds']);
                }
                break;
        }


    }

    private function ensureJoinsForCounting(QueryBuilder $qb, array $filters, array $excludeFilters): void
    {
        // Joiny jsou už přidané v metodách getBreedsWithCounts, getRegionsWithCounts, getPostTypesWithCounts
        // Tato metoda už není potřeba, ale necháváme ji pro kompatibilitu
    }

    public function getCategoriesWithCounts(array $currentFilters = []): array
    {
        $qb = $this->getEntityManager()->createQueryBuilder()
            ->select('c.id, c.name, c.slug, COUNT(p.id) as postCount')
            ->from(Category::class, 'c')
            ->leftJoin('c.posts', 'p')
            // Přidáme joiny, které budeme potřebovat pro filtrování
            ->leftJoin('p.breed', 'b')
            ->leftJoin('p.region', 'r')
            ->where('p.isActive = :isActive')
            ->setParameter('isActive', true)
            ->groupBy('c.id, c.name, c.slug')
            ->orderBy('c.name', 'ASC');

        // Aplikujeme ostatní filtry, ale ignorujeme filtr kategorie
        $this->applyFacetFilters($qb, $currentFilters, 'category');

        return $qb->getQuery()->getResult();
    }

    public function getTotalActivePostsCount(): int
    {
        return (int) $this->createQueryBuilder('p')
            ->select('COUNT(p.id)')
            ->where('p.isActive = :isActive')
            ->setParameter('isActive', true)
            ->getQuery()
            ->getSingleScalarResult();
    }

    private function hasJoin(QueryBuilder $qb, string $alias): bool
    {
        $joins = $qb->getDQLPart('join');
        foreach ($joins as $rootAlias => $joinArray) {
            foreach ($joinArray as $join) {
                if ($join->getAlias() === $alias) {
                    return true;
                }
            }
        }
        return false;
    }
}
