<?php

namespace App\Service;

use App\Entity\Category;
use App\Entity\Breed;
use App\Entity\Region;
use App\Entity\Enum\PostType;
use App\Repository\PostRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Request;

class PostFilterService
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private PostRepository $postRepository
    ) {
    }

    public function getFiltersFromRequest(Request $request, ?Category $category = null): array
    {
        $filters = [];

        // Kategorie z URL parametru
        if ($category) {
            $filters['category'] = $category;
        }

        // Plemena z query parametrů
        $breedIds = $request->query->all('breeds');
        if (!empty($breedIds) && is_array($breedIds)) {
            $breeds = $this->entityManager->getRepository(Breed::class)->findBy([
                'id' => array_filter($breedIds, 'is_numeric')
            ]);
            if (!empty($breeds)) {
                $filters['breeds'] = $breeds;
            }
        }

        // Kraje z query parametrů
        $regionIds = $request->query->all('regions');
        if (!empty($regionIds) && is_array($regionIds)) {
            $regions = $this->entityManager->getRepository(Region::class)->findBy([
                'id' => array_filter($regionIds, 'is_numeric')
            ]);
            if (!empty($regions)) {
                $filters['regions'] = $regions;
            }
        }

        // Typy inzerátů z query parametrů
        $types = $request->query->all('types');
        if (!empty($types) && is_array($types)) {
            $validTypes = [];
            foreach ($types as $type) {
                try {
                    $validTypes[] = PostType::from($type);
                } catch (\ValueError $e) {
                    // Ignorovat neplatné typy
                }
            }
            if (!empty($validTypes)) {
                $filters['types'] = $validTypes;
            }
        }

        // Cenové rozmezí
        $priceFrom = $request->query->get('priceFrom');
        if ($priceFrom !== null && is_numeric($priceFrom)) {
            $filters['priceFrom'] = (float) $priceFrom;
        }

        $priceTo = $request->query->get('priceTo');
        if ($priceTo !== null && is_numeric($priceTo)) {
            $filters['priceTo'] = (float) $priceTo;
        }

        // Textové vyhledávání
        $search = $request->query->get('search');
        if (!empty($search)) {
            $filters['search'] = trim($search);
        }

        return $filters;
    }

    public function getFilterData(?Category $selectedCategory = null, array $currentFilters = []): array
    {
        // Kategorie s počty - PŘEDÁME AKTUÁLNÍ FILTRY
        $categoriesWithCounts = $this->postRepository->getCategoriesWithCounts($currentFilters);

        // Všechny kategorie pro základní seznam
        $allCategories = $this->entityManager->getRepository(Category::class)->findBy([], ['name' => 'ASC']);

        // Celkový počet všech aktivních inzerátů
        $totalCount = $this->postRepository->getTotalActivePostsCount();

        // Spojit kategorie s počty
        $categories = [];
        foreach ($allCategories as $category) {
            $count = 0;
            foreach ($categoriesWithCounts as $categoryData) {
                if ($categoryData['id'] == $category->getId()) {
                    $count = (int) $categoryData['postCount'];
                    break;
                }
            }
            $categories[] = [
                'entity' => $category,
                'count' => $count
            ];
        }

        // Plemena s počty podle vybrané kategorie
        $breedsWithCounts = $this->postRepository->getBreedsWithCounts($selectedCategory, $currentFilters);
        $breeds = [];
        foreach ($breedsWithCounts as $breedData) {
            $breed = $this->entityManager->getRepository(Breed::class)->find($breedData['id']);
            if ($breed) {
                $breeds[] = [
                    'entity' => $breed,
                    'count' => (int) $breedData['postCount']
                ];
            }
        }

        // Kraje s počty
        $regionsWithCounts = $this->postRepository->getRegionsWithCounts($currentFilters);
        $regions = [];
        foreach ($regionsWithCounts as $regionData) {
            $region = $this->entityManager->getRepository(Region::class)->find($regionData['id']);
            if ($region) {
                $regions[] = [
                    'entity' => $region,
                    'count' => (int) $regionData['postCount']
                ];
            }
        }

        // Typy inzerátů s počty
        $postTypesWithCounts = $this->postRepository->getPostTypesWithCounts($currentFilters);

        // Cenové rozmezí
        $priceRange = $this->postRepository->getPriceRange();

        return [
            'categories' => $categories,
            'breeds' => $breeds,
            'regions' => $regions,
            'postTypes' => $postTypesWithCounts,
            'priceRange' => $priceRange,
            'selectedCategory' => $selectedCategory,
            'totalCount' => $totalCount
        ];
    }

    public function buildFilterUrl(string $baseUrl, array $filters, ?Category $category = null): string
    {
        $url = $baseUrl;

        // Přidat kategorii do URL
        if ($category) {
            $url .= '/' . $category->getSlug();
        }

        $queryParams = [];

        // Plemena
        if (!empty($filters['breeds'])) {
            foreach ($filters['breeds'] as $breed) {
                $queryParams['breeds'][] = $breed->getId();
            }
        }

        // Kraje
        if (!empty($filters['regions'])) {
            foreach ($filters['regions'] as $region) {
                $queryParams['regions'][] = $region->getId();
            }
        }

        // Typy inzerátů
        if (!empty($filters['types'])) {
            foreach ($filters['types'] as $type) {
                $queryParams['types'][] = $type->value;
            }
        }

        // Ceny
        if (!empty($filters['priceFrom'])) {
            $queryParams['priceFrom'] = $filters['priceFrom'];
        }

        if (!empty($filters['priceTo'])) {
            $queryParams['priceTo'] = $filters['priceTo'];
        }

        // Vyhledávání
        if (!empty($filters['search'])) {
            $queryParams['search'] = $filters['search'];
        }

        if (!empty($queryParams)) {
            $url .= '?' . http_build_query($queryParams);
        }

        return $url;
    }

    public function getActiveFiltersCount(array $filters): int
    {
        $count = 0;

        if (!empty($filters['breeds'])) {
            $count += count($filters['breeds']);
        }

        if (!empty($filters['regions'])) {
            $count += count($filters['regions']);
        }

        if (!empty($filters['types'])) {
            $count += count($filters['types']);
        }

        if (!empty($filters['priceFrom']) || !empty($filters['priceTo'])) {
            $count++;
        }

        if (!empty($filters['search'])) {
            $count++;
        }

        return $count;
    }
}
