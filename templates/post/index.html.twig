{% extends 'base.html.twig' %}

{% block title %}
    {% if currentCategory %}
        {{ currentCategory.name }} - Inzeráty - Infauna
    {% else %}
        Inzeráty - Infauna
    {% endif %}
{% endblock %}

{% block body %}
<div class="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
    <!-- Header -->
    <div class="bg-white/80 backdrop-blur-sm border-b border-slate-200/60 sticky top-0 z-40">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2">
                        <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold text-sm">🐾</span>
                        </div>
                        <h1 class="text-2xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent">
                            {% if currentCategory %}
                                {{ currentCategory.name }}
                            {% else %}
                                Všechny inzeráty
                            {% endif %}
                        </h1>
                    </div>
                    <div class="hidden sm:block">
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800" data-filter-target="count">
                            {% if totalCount == 1 %}
                                {{ totalCount }} inzerát
                            {% elseif totalCount < 5 %}
                                {{ totalCount }} inzeráty
                            {% else %}
                                {{ totalCount }} inzerátů
                            {% endif %}
                        </span>
                        {% if activeFiltersCount > 0 %}
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-amber-100 text-amber-800 ml-2">
                                {{ activeFiltersCount }} {% if activeFiltersCount == 1 %}filtr{% elseif activeFiltersCount < 5 %}filtry{% else %}filtrů{% endif %}
                            </span>
                        {% endif %}
                    </div>
                </div>
                <div>
                    <a href="{{ path('app_home') }}" class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white text-sm font-medium rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                        </svg>
                        Domů
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Content with Sidebar -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="flex flex-col lg:flex-row gap-8"
             data-controller="filter"
             data-filter-base-url-value="{{ path('app_posts') }}"
             data-filter-current-category-value="{{ currentCategory ? currentCategory.slug : '' }}">

            <!-- Sidebar s filtry -->
            <div class="lg:w-80 flex-shrink-0">
                <div class="bg-white/70 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6 sticky top-24">
                    <div class="flex items-center justify-between mb-8">
                        <div class="flex items-center space-x-2">
                            <div class="w-6 h-6 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                                <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.207A1 1 0 013 6.5V4z"/>
                                </svg>
                            </div>
                            <h3 class="text-lg font-semibold text-slate-800">Filtry</h3>
                        </div>
                        {% if activeFiltersCount > 0 %}
                            <button type="button"
                                    onclick="this.closest('[data-controller=filter]').querySelector('[data-action=filter#clearAllFilters]').click()"
                                    class="text-sm text-red-600 hover:text-red-700 font-medium transition-colors">
                                Vymazat vše
                            </button>
                        {% endif %}
                    </div>

                    <form data-filter-target="form" class="space-y-8">
                        <!-- Kategorie -->
                        <div class="bg-gradient-to-r from-slate-50 to-blue-50 rounded-xl p-4 border border-slate-200/50">
                            <h4 class="text-sm font-semibold text-slate-700 mb-4 flex items-center">
                                <span class="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                                Kategorie
                            </h4>
                            <div class="space-y-3">
                                <label class="group flex items-center justify-between p-3 rounded-lg hover:bg-white/60 transition-all cursor-pointer">
                                    <div class="flex items-center">
                                        <input type="radio"
                                               name="category"
                                               value="all"
                                               data-filter-target="categoryRadio"
                                               {% if not currentCategory %}checked{% endif %}
                                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-slate-300">
                                        <span class="ml-3 text-sm font-medium text-slate-700 group-hover:text-slate-900">Všechny kategorie</span>
                                    </div>
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-slate-100 text-slate-600">{{ filterData.totalCount }}</span>
                                </label>

                                {% for categoryData in filterData.categories %}
                                    {% set category = categoryData.entity %}
                                    {% set count = categoryData.count %}
                                    <label class="group flex items-center justify-between p-3 rounded-lg hover:bg-white/60 transition-all cursor-pointer {% if count == 0 %}opacity-50{% endif %}">
                                        <div class="flex items-center">
                                            <input type="radio"
                                                   name="category"
                                                   value="{{ category.slug }}"
                                                   data-filter-target="categoryRadio"
                                                   {% if currentCategory and currentCategory.id == category.id %}checked{% endif %}
                                                   {% if count == 0 %}disabled{% endif %}
                                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-slate-300">
                                            <span class="ml-3 text-sm font-medium {% if count == 0 %}text-slate-400{% else %}text-slate-700 group-hover:text-slate-900{% endif %}">
                                                {% if category.name == 'Psi' %}🐕 {% elseif category.name == 'Kočky' %}🐱 {% elseif category.name == 'Ptáci' %}🐦 {% endif %}{{ category.name }}
                                            </span>
                                        </div>
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {% if count == 0 %}bg-slate-100 text-slate-400{% else %}bg-blue-100 text-blue-600{% endif %}">{{ count }}</span>
                                    </label>
                                {% endfor %}
                            </div>
                        </div>

                        <!-- Plemena -->
                        {% if filterData.breeds|length > 0 %}
                            <div class="bg-gradient-to-r from-emerald-50 to-teal-50 rounded-xl p-4 border border-emerald-200/50">
                                <h4 class="text-sm font-semibold text-slate-700 mb-4 flex items-center">
                                    <span class="w-2 h-2 bg-emerald-500 rounded-full mr-2"></span>
                                    Plemena
                                </h4>
                                <div class="space-y-2 max-h-48 overflow-y-auto scrollbar-thin scrollbar-thumb-slate-300 scrollbar-track-transparent">
                                    {% for breedData in filterData.breeds %}
                                        {% set breed = breedData.entity %}
                                        {% set count = breedData.count %}
                                        <label class="group flex items-center justify-between p-2 rounded-lg hover:bg-white/60 transition-all cursor-pointer filter-option {% if count == 0 %}opacity-50{% endif %}" data-category="{{ breed.category.slug }}">
                                            <div class="flex items-center">
                                                <input type="checkbox"
                                                       name="breeds[]"
                                                       value="{{ breed.id }}"
                                                       data-filter-target="breedCheckbox"
                                                       data-category="{{ breed.category.slug }}"
                                                       {% if currentFilters.breeds is defined %}
                                                           {% for selectedBreed in currentFilters.breeds %}
                                                               {% if selectedBreed.id == breed.id %}checked{% endif %}
                                                           {% endfor %}
                                                       {% endif %}
                                                       {% if count == 0 %}disabled{% endif %}
                                                       class="h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-slate-300 rounded">
                                                <span class="ml-3 text-sm {% if count == 0 %}text-slate-400{% else %}text-slate-700 group-hover:text-slate-900{% endif %}">{{ breed.name }}</span>
                                            </div>
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {% if count == 0 %}bg-slate-100 text-slate-400{% else %}bg-emerald-100 text-emerald-600{% endif %}">{{ count }}</span>
                                        </label>
                                    {% endfor %}
                                </div>
                            </div>
                        {% endif %}

                        <!-- Kraje -->
                        {% if filterData.regions|length > 0 %}
                            <div class="bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl p-4 border border-purple-200/50">
                                <h4 class="text-sm font-semibold text-slate-700 mb-4 flex items-center">
                                    <span class="w-2 h-2 bg-purple-500 rounded-full mr-2"></span>
                                    Kraje
                                </h4>
                                <div class="space-y-2 max-h-48 overflow-y-auto scrollbar-thin scrollbar-thumb-slate-300 scrollbar-track-transparent">
                                    {% for regionData in filterData.regions %}
                                        {% set region = regionData.entity %}
                                        {% set count = regionData.count %}
                                        <label class="group flex items-center justify-between p-2 rounded-lg hover:bg-white/60 transition-all cursor-pointer {% if count == 0 %}opacity-50{% endif %}">
                                            <div class="flex items-center">
                                                <input type="checkbox"
                                                       name="regions[]"
                                                       value="{{ region.id }}"
                                                       data-filter-target="regionCheckbox"
                                                       {% if currentFilters.regions is defined %}
                                                           {% for selectedRegion in currentFilters.regions %}
                                                               {% if selectedRegion.id == region.id %}checked{% endif %}
                                                           {% endfor %}
                                                       {% endif %}
                                                       {% if count == 0 %}disabled{% endif %}
                                                       class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-slate-300 rounded">
                                                <span class="ml-3 text-sm {% if count == 0 %}text-slate-400{% else %}text-slate-700 group-hover:text-slate-900{% endif %}">📍 {{ region.name }}</span>
                                            </div>
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {% if count == 0 %}bg-slate-100 text-slate-400{% else %}bg-purple-100 text-purple-600{% endif %}">{{ count }}</span>
                                        </label>
                                    {% endfor %}
                                </div>
                            </div>
                        {% endif %}

                        <!-- Typ inzerátu -->
                        <div class="bg-gradient-to-r from-orange-50 to-red-50 rounded-xl p-4 border border-orange-200/50">
                            <h4 class="text-sm font-semibold text-slate-700 mb-4 flex items-center">
                                <span class="w-2 h-2 bg-orange-500 rounded-full mr-2"></span>
                                Typ inzerátu
                            </h4>
                            <div class="space-y-3">
                                {% for postTypeData in filterData.postTypes %}
                                    {% set postType = postTypeData.type %}
                                    {% set count = postTypeData.count %}
                                    <label class="group flex items-center justify-between p-3 rounded-lg hover:bg-white/60 transition-all cursor-pointer {% if count == 0 %}opacity-50{% endif %}">
                                        <div class="flex items-center">
                                            <input type="checkbox"
                                                   name="types[]"
                                                   value="{{ postType.value }}"
                                                   data-filter-target="typeCheckbox"
                                                   {% if currentFilters.types is defined %}
                                                       {% for selectedType in currentFilters.types %}
                                                           {% if selectedType.value == postType.value %}checked{% endif %}
                                                       {% endfor %}
                                                   {% endif %}
                                                   {% if count == 0 %}disabled{% endif %}
                                                   class="h-4 w-4 text-orange-600 focus:ring-orange-500 border-slate-300 rounded">
                                            <span class="ml-3 text-sm font-medium {% if count == 0 %}text-slate-400{% else %}text-slate-700 group-hover:text-slate-900{% endif %}">
                                                {% if postType.value == 'sell' %}💰 {% elseif postType.value == 'buy' %}🛒 {% elseif postType.value == 'give' %}🎁 {% endif %}{{ postType.label }}
                                            </span>
                                        </div>
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {% if count == 0 %}bg-slate-100 text-slate-400{% else %}bg-orange-100 text-orange-600{% endif %}">{{ count }}</span>
                                    </label>
                                {% endfor %}
                            </div>
                        </div>



                        <!-- Skryté tlačítko pro vymazání filtrů -->
                        <button type="button"
                                data-action="filter#clearAllFilters"
                                class="hidden">
                        </button>
                    </form>
                </div>
            </div>

            <!-- Hlavní obsah -->
            <div class="flex-1">
                <div data-filter-target="results">
                    {% if posts|length > 0 %}
                        <div class="space-y-4">
                            {% for post in posts %}
                                <article class="bg-white/70 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300 hover:-translate-y-1 overflow-hidden">
                                    <div class="flex">
                                        <!-- Obrázek -->
                                        <div class="w-48 h-32 flex-shrink-0">
                                            {% if post.mainImage %}
                                                <img src="{{ post.mainImage.imageUrl }}" alt="{{ post.title }}" class="w-full h-full object-cover">
                                            {% else %}
                                                <div class="w-full h-full bg-gradient-to-br from-blue-400 via-purple-500 to-pink-500 flex items-center justify-center">
                                                    <div class="text-white text-center">
                                                        <div class="text-3xl mb-1">
                                                            {% if post.category.name == 'Psi' %}🐕
                                                            {% elseif post.category.name == 'Kočky' %}🐱
                                                            {% elseif post.category.name == 'Ptáci' %}🐦
                                                            {% else %}🐾
                                                            {% endif %}
                                                        </div>
                                                        <p class="text-xs font-medium">{{ post.category.name }}</p>
                                                    </div>
                                                </div>
                                            {% endif %}
                                        </div>

                                        <!-- Obsah -->
                                        <div class="flex-1 p-6">
                                            <div class="flex items-start justify-between mb-3">
                                                <div class="flex items-center space-x-2">
                                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold
                                                        {% if post.postType.value == 'sell' %}bg-emerald-100 text-emerald-700
                                                        {% elseif post.postType.value == 'buy' %}bg-blue-100 text-blue-700
                                                        {% else %}bg-purple-100 text-purple-700
                                                        {% endif %}">
                                                        {% if post.postType.value == 'sell' %}💰
                                                        {% elseif post.postType.value == 'buy' %}🛒
                                                        {% else %}🎁
                                                        {% endif %}{{ post.postType.label }}
                                                    </span>
                                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-slate-100 text-slate-600">
                                                        {% if post.category.name == 'Psi' %}🐕 {% elseif post.category.name == 'Kočky' %}🐱 {% elseif post.category.name == 'Ptáci' %}🐦 {% endif %}{{ post.category.name }}
                                                    </span>
                                                </div>
                                                {% if post.formattedPrice %}
                                                    <div class="text-right">
                                                        <span class="text-2xl font-bold bg-gradient-to-r from-emerald-600 to-blue-600 bg-clip-text text-transparent">{{ post.formattedPrice }}</span>
                                                    </div>
                                                {% endif %}
                                            </div>

                                            <h3 class="text-xl font-bold text-slate-800 mb-2 line-clamp-1">
                                                <a href="{{ path('app_post_detail', {slug: post.slug}) }}" class="hover:text-blue-600 transition-colors">
                                                    {{ post.title }}
                                                </a>
                                            </h3>

                                            <p class="text-slate-600 text-sm mb-4 line-clamp-2">{{ post.description|slice(0, 120) }}{% if post.description|length > 120 %}...{% endif %}</p>

                                            <div class="flex items-center justify-between">
                                                <div class="flex items-center space-x-4 text-sm text-slate-500">
                                                    <span class="flex items-center">
                                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                                                        </svg>
                                                        {{ post.region ? post.region.name : post.country.label }}
                                                    </span>
                                                    {% if post.breed %}
                                        <span class="flex items-center">
                                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"/>
                                            </svg>
                                            {{ post.breed.name }}
                                        </span>
                                    {% endif %}
                                                    <span class="flex items-center">
                                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                                        </svg>
                                                        {{ post.createdAt|date('d.m.Y') }}
                                                    </span>
                                                </div>
                                                <a href="{{ path('app_post_detail', {slug: post.slug}) }}"
                                                   class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white text-sm font-medium rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                                                    Zobrazit detail
                                                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                                                    </svg>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </article>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="bg-white/70 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-12 text-center">
                            <div class="w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-slate-100 to-slate-200 rounded-full flex items-center justify-center">
                                <svg class="w-12 h-12 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                                </svg>
                            </div>
                            <h3 class="text-xl font-bold text-slate-800 mb-3">Žádné inzeráty</h3>
                            <p class="text-slate-600 mb-6 max-w-md mx-auto">
                                {% if activeFiltersCount > 0 %}
                                    Žádné inzeráty nevyhovují vybraným filtrům. Zkuste změnit kritéria nebo vymazat některé filtry.
                                {% else %}
                                    Momentálně nejsou k dispozici žádné aktivní inzeráty v této kategorii.
                                {% endif %}
                            </p>
                            {% if activeFiltersCount > 0 %}
                                <button type="button"
                                        onclick="this.closest('[data-controller=filter]').querySelector('[data-action=filter#clearAllFilters]').click()"
                                        class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                                    </svg>
                                    Vymazat všechny filtry
                                </button>
                            {% endif %}
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
