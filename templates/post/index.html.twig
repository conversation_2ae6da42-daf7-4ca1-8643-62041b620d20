{% extends 'base.html.twig' %}

{% block title %}
    {% if currentCategory %}
        {{ currentCategory.name }} - Inzeráty - Infauna
    {% else %}
        Inzeráty - Infauna
    {% endif %}
{% endblock %}

{% block body %}
<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">
                        {% if currentCategory %}
                            {{ currentCategory.name }}
                        {% else %}
                            Inzeráty
                        {% endif %}
                    </h1>
                    <p class="mt-1 text-sm text-gray-500" data-filter-target="count">
                        {% if totalCount == 1 %}
                            Nalezen {{ totalCount }} inzerát
                        {% elseif totalCount < 5 %}
                            Nalezeny {{ totalCount }} inzeráty
                        {% else %}
                            Nalezeno {{ totalCount }} inzerátů
                        {% endif %}
                        {% if activeFiltersCount > 0 %}
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800 ml-2">
                                {{ activeFiltersCount }} {% if activeFiltersCount == 1 %}filtr{% elseif activeFiltersCount < 5 %}filtry{% else %}filtrů{% endif %}
                            </span>
                        {% endif %}
                    </p>
                </div>
                <div>
                    <a href="{{ path('app_home') }}" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium transition duration-150 ease-in-out">
                        ← Zpět na hlavní stránku
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Content with Sidebar -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="flex flex-col lg:flex-row gap-8"
             data-controller="filter"
             data-filter-base-url-value="{{ path('app_posts') }}"
             data-filter-current-category-value="{{ currentCategory ? currentCategory.slug : '' }}">

            <!-- Sidebar s filtry -->
            <div class="lg:w-1/4">
                <div class="bg-white rounded-lg shadow p-6 sticky top-8">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-medium text-gray-900">Filtry</h3>
                        {% if activeFiltersCount > 0 %}
                            <button type="button"
                                    onclick="this.closest('[data-controller=filter]').querySelector('[data-action=filter#clearAllFilters]').click()"
                                    class="text-sm text-indigo-600 hover:text-indigo-500">
                                Vymazat vše
                            </button>
                        {% endif %}
                    </div>

                    <form data-filter-target="form" class="space-y-6">
                        <!-- Kategorie -->
                        <div>
                            <h4 class="text-sm font-medium text-gray-900 mb-3">Kategorie</h4>
                            <div class="space-y-2">
                                <label class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <input type="radio"
                                               name="category"
                                               value="all"
                                               data-filter-target="categoryRadio"
                                               {% if not currentCategory %}checked{% endif %}
                                               class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300">
                                        <span class="ml-2 text-sm text-gray-700">Všechny kategorie</span>
                                    </div>
                                    <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">{{ filterData.totalCount }}</span>
                                </label>
                                {% for categoryData in filterData.categories %}
                                    {% set category = categoryData.entity %}
                                    {% set count = categoryData.count %}
                                    <label class="flex items-center justify-between {% if count == 0 %}opacity-50{% endif %}">
                                        <div class="flex items-center">
                                            <input type="radio"
                                                   name="category"
                                                   value="{{ category.slug }}"
                                                   data-filter-target="categoryRadio"
                                                   {% if currentCategory and currentCategory.id == category.id %}checked{% endif %}
                                                   {% if count == 0 %}disabled{% endif %}
                                                   class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 {% if count == 0 %}cursor-not-allowed{% endif %}">
                                            <span class="ml-2 text-sm text-gray-700">{{ category.name }}</span>
                                        </div>
                                        <span class="text-xs {% if count == 0 %}text-gray-400{% else %}text-gray-500{% endif %} bg-gray-100 px-2 py-1 rounded-full">{{ count }}</span>
                                    </label>
                                {% endfor %}
                            </div>
                        </div>

                        <!-- Plemena -->
                        {% if filterData.breeds|length > 0 %}
                            <div>
                                <h4 class="text-sm font-medium text-gray-900 mb-3">Plemena</h4>
                                <div class="space-y-2 max-h-48 overflow-y-auto filter-scroll">
                                    {% for breedData in filterData.breeds %}
                                        {% set breed = breedData.entity %}
                                        {% set count = breedData.count %}
                                        <label class="flex items-center justify-between filter-option {% if count == 0 %}opacity-50{% endif %}" data-category="{{ breed.category.slug }}">
                                            <div class="flex items-center">
                                                <input type="checkbox"
                                                       name="breeds[]"
                                                       value="{{ breed.id }}"
                                                       data-filter-target="breedCheckbox"
                                                       data-category="{{ breed.category.slug }}"
                                                       {% if currentFilters.breeds is defined %}
                                                           {% for selectedBreed in currentFilters.breeds %}
                                                               {% if selectedBreed.id == breed.id %}checked{% endif %}
                                                           {% endfor %}
                                                       {% endif %}
                                                       {% if count == 0 %}disabled{% endif %}
                                                       class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded {% if count == 0 %}cursor-not-allowed{% endif %}">
                                                <span class="ml-2 text-sm text-gray-700">{{ breed.name }}</span>
                                            </div>
                                            <span class="text-xs {% if count == 0 %}text-gray-400{% else %}text-gray-500{% endif %} bg-gray-100 px-2 py-1 rounded-full">{{ count }}</span>
                                        </label>
                                    {% endfor %}
                                </div>
                            </div>
                        {% endif %}

                        <!-- Kraje -->
                        {% if filterData.regions|length > 0 %}
                            <div>
                                <h4 class="text-sm font-medium text-gray-900 mb-3">Kraj</h4>
                                <div class="space-y-2 max-h-48 overflow-y-auto filter-scroll">
                                    {% for regionData in filterData.regions %}
                                        {% set region = regionData.entity %}
                                        {% set count = regionData.count %}
                                        <label class="flex items-center justify-between {% if count == 0 %}opacity-50{% endif %}">
                                            <div class="flex items-center">
                                                <input type="checkbox"
                                                       name="regions[]"
                                                       value="{{ region.id }}"
                                                       data-filter-target="regionCheckbox"
                                                       {% if currentFilters.regions is defined %}
                                                           {% for selectedRegion in currentFilters.regions %}
                                                               {% if selectedRegion.id == region.id %}checked{% endif %}
                                                           {% endfor %}
                                                       {% endif %}
                                                       {% if count == 0 %}disabled{% endif %}
                                                       class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded {% if count == 0 %}cursor-not-allowed{% endif %}">
                                                <span class="ml-2 text-sm text-gray-700">{{ region.name }}</span>
                                            </div>
                                            <span class="text-xs {% if count == 0 %}text-gray-400{% else %}text-gray-500{% endif %} bg-gray-100 px-2 py-1 rounded-full">{{ count }}</span>
                                        </label>
                                    {% endfor %}
                                </div>
                            </div>
                        {% endif %}

                        <!-- Typ inzerátu -->
                        <div>
                            <h4 class="text-sm font-medium text-gray-900 mb-3">Typ inzerátu</h4>
                            <div class="space-y-2">
                                {% for postTypeData in filterData.postTypes %}
                                    {% set postType = postTypeData.type %}
                                    {% set count = postTypeData.count %}
                                    <label class="flex items-center justify-between {% if count == 0 %}opacity-50{% endif %}">
                                        <div class="flex items-center">
                                            <input type="checkbox"
                                                   name="types[]"
                                                   value="{{ postType.value }}"
                                                   data-filter-target="typeCheckbox"
                                                   {% if currentFilters.types is defined %}
                                                       {% for selectedType in currentFilters.types %}
                                                           {% if selectedType.value == postType.value %}checked{% endif %}
                                                       {% endfor %}
                                                   {% endif %}
                                                   {% if count == 0 %}disabled{% endif %}
                                                   class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded {% if count == 0 %}cursor-not-allowed{% endif %}">
                                            <span class="ml-2 text-sm text-gray-700">{{ postType.label }}</span>
                                        </div>
                                        <span class="text-xs {% if count == 0 %}text-gray-400{% else %}text-gray-500{% endif %} bg-gray-100 px-2 py-1 rounded-full">{{ count }}</span>
                                    </label>
                                {% endfor %}
                            </div>
                        </div>

                        <!-- Cenové rozmezí -->
                        {% if filterData.priceRange.max > 0 %}
                            <div>
                                <h4 class="text-sm font-medium text-gray-900 mb-3">Cena (Kč)</h4>
                                <div class="space-y-3">
                                    <div>
                                        <label class="block text-xs text-gray-500 mb-1">Od</label>
                                        <input type="number"
                                               name="priceFrom"
                                               value="{{ currentFilters.priceFrom ?? '' }}"
                                               min="0"
                                               max="{{ filterData.priceRange.max }}"
                                               placeholder="0"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-indigo-500 focus:border-indigo-500">
                                    </div>
                                    <div>
                                        <label class="block text-xs text-gray-500 mb-1">Do</label>
                                        <input type="number"
                                               name="priceTo"
                                               value="{{ currentFilters.priceTo ?? '' }}"
                                               min="0"
                                               max="{{ filterData.priceRange.max }}"
                                               placeholder="{{ filterData.priceRange.max }}"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-indigo-500 focus:border-indigo-500">
                                    </div>
                                </div>
                            </div>
                        {% endif %}

                        <!-- Vyhledávání -->
                        <div>
                            <h4 class="text-sm font-medium text-gray-900 mb-3">Vyhledávání</h4>
                            <input type="text"
                                   name="search"
                                   value="{{ currentFilters.search ?? '' }}"
                                   placeholder="Hledat v názvu nebo popisu..."
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-indigo-500 focus:border-indigo-500">
                        </div>

                        <!-- Skryté tlačítko pro vymazání filtrů -->
                        <button type="button"
                                data-action="filter#clearAllFilters"
                                class="hidden">
                        </button>
                    </form>
                </div>
            </div>

            <!-- Hlavní obsah -->
            <div class="lg:w-3/4">
                <div data-filter-target="results">
                    {% if posts|length > 0 %}
                        <div class="grid gap-6 posts-grid">
                            {% for post in posts %}
                    <div class="bg-white overflow-hidden shadow rounded-lg hover:shadow-lg transition-shadow duration-300">
                        {% if post.mainImage %}
                            <div class="h-48 bg-gray-200 flex items-center justify-center">
                                <img src="{{ post.mainImage.imageUrl }}" alt="{{ post.title }}" class="h-full w-full object-cover">
                            </div>
                        {% else %}
                            <div class="h-48 bg-gradient-to-r from-blue-400 to-purple-500 flex items-center justify-center">
                                <div class="text-white text-center">
                                    <div class="text-4xl mb-2">
                                        {% if post.category.name == 'Psi' %}🐕
                                        {% elseif post.category.name == 'Kočky' %}🐱
                                        {% elseif post.category.name == 'Ptáci' %}🐦
                                        {% else %}🐾
                                        {% endif %}
                                    </div>
                                    <p class="text-sm">{{ post.category.name }}</p>
                                </div>
                            </div>
                        {% endif %}

                        <div class="p-6">
                            <div class="flex items-center justify-between mb-2">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    {% if post.postType.value == 'prodám' %}bg-green-100 text-green-800
                                    {% elseif post.postType.value == 'koupím' %}bg-blue-100 text-blue-800
                                    {% else %}bg-purple-100 text-purple-800
                                    {% endif %}">
                                    {{ post.postType.label }}
                                </span>
                                {% if post.formattedPrice %}
                                    <span class="text-lg font-bold text-gray-900">{{ post.formattedPrice }}</span>
                                {% endif %}
                            </div>

                            <h3 class="text-lg font-medium text-gray-900 mb-2">
                                <a href="{{ path('app_post_detail', {slug: post.slug}) }}" class="hover:text-indigo-600">
                                    {{ post.title }}
                                </a>
                            </h3>

                            <div class="text-sm text-gray-500 mb-3">
                                <div class="flex items-center space-x-4">
                                    <span>📍 {{ post.region ? post.region.name : post.country.label }}</span>
                                    {% if post.breed %}
                                        <span>🏷️ {{ post.breed.name }}</span>
                                    {% endif %}
                                </div>
                            </div>

                            <p class="text-gray-600 text-sm line-clamp-3">
                                {{ post.description|length > 100 ? post.description|slice(0, 100) ~ '...' : post.description }}
                            </p>

                            <div class="mt-4 flex items-center justify-between">
                                <span class="text-xs text-gray-400">
                                    {{ post.createdAt|date('d.m.Y H:i') }}
                                </span>
                                <a href="{{ path('app_post_detail', {slug: post.slug}) }}"
                                   class="text-indigo-600 hover:text-indigo-500 text-sm font-medium">
                                    Zobrazit detail →
                                </a>
                            </div>
                        </div>
                    </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-12">
                            <div class="text-6xl mb-4">🔍</div>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">Žádné inzeráty</h3>
                            <p class="text-gray-500">
                                {% if activeFiltersCount > 0 %}
                                    Žádné inzeráty nevyhovují vybraným filtrům. Zkuste změnit kritéria vyhledávání.
                                {% else %}
                                    Momentálně nejsou k dispozici žádné aktivní inzeráty.
                                {% endif %}
                            </p>
                            {% if activeFiltersCount > 0 %}
                                <button type="button"
                                        onclick="this.closest('[data-controller=filter]').querySelector('[data-action=filter#clearAllFilters]').click()"
                                        class="mt-4 bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium transition duration-150 ease-in-out">
                                    Vymazat všechny filtry
                                </button>
                            {% endif %}
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
