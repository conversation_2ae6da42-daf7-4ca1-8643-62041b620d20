{% extends 'base.html.twig' %}

{% block title %}{{ post.title }} - Infauna{% endblock %}

{% block body %}
<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div>
                    <nav class="flex" aria-label="Breadcrumb">
                        <ol class="flex items-center space-x-4">
                            <li>
                                <a href="{{ path('app_home') }}" class="text-gray-400 hover:text-gray-500">
                                    <PERSON><PERSON>
                                </a>
                            </li>
                            <li>
                                <div class="flex items-center">
                                    <svg class="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                                    </svg>
                                    <a href="{{ path('app_posts_category', {categorySlug: post.category.slug}) }}" class="ml-4 text-gray-400 hover:text-gray-500">
                                        {{ post.category.name }}
                                    </a>
                                </div>
                            </li>
                            <li>
                                <div class="flex items-center">
                                    <svg class="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                                    </svg>
                                    <span class="ml-4 text-gray-500">{{ post.title }}</span>
                                </div>
                            </li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="{{ path('app_posts_category', {categorySlug: post.category.slug}) }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium transition duration-150 ease-in-out">
                        ← Zpět na {{ post.category.name|lower }}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="bg-white shadow overflow-hidden sm:rounded-lg">
            <div class="px-4 py-5 sm:px-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">{{ post.title }}</h1>
                        <div class="mt-2 flex items-center space-x-4">
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                                {% if post.postType.value == 'prodám' %}bg-green-100 text-green-800
                                {% elseif post.postType.value == 'koupím' %}bg-blue-100 text-blue-800
                                {% else %}bg-purple-100 text-purple-800
                                {% endif %}">
                                {{ post.postType.label }}
                            </span>
                            <span class="text-sm text-gray-500">
                                📅 {{ post.createdAt|date('d.m.Y H:i') }}
                            </span>
                        </div>
                    </div>
                    {% if post.formattedPrice %}
                        <div class="text-right">
                            <div class="text-3xl font-bold text-gray-900">{{ post.formattedPrice }}</div>
                        </div>
                    {% endif %}
                </div>
            </div>

            <div class="border-t border-gray-200">
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 p-6">
                    <!-- Hlavní obsah -->
                    <div class="lg:col-span-2">
                        <!-- Obrázky -->
                        {% if post.images|length > 0 %}
                            <div class="mb-6">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    {% for image in post.images %}
                                        <div class="aspect-w-16 aspect-h-12">
                                            <img src="{{ image.imageUrl }}" alt="{{ post.title }}"
                                                 class="w-full h-64 object-cover rounded-lg shadow-md">
                                        </div>
                                    {% endfor %}
                                </div>
                            </div>
                        {% else %}
                            <div class="mb-6">
                                <div class="h-64 bg-gradient-to-r from-blue-400 to-purple-500 rounded-lg flex items-center justify-center">
                                    <div class="text-white text-center">
                                        <div class="text-6xl mb-4">
                                            {% if post.category.name == 'Psi' %}🐕
                                            {% elseif post.category.name == 'Kočky' %}🐱
                                            {% elseif post.category.name == 'Ptáci' %}🐦
                                            {% else %}🐾
                                            {% endif %}
                                        </div>
                                        <p class="text-xl">{{ post.category.name }}</p>
                                    </div>
                                </div>
                            </div>
                        {% endif %}

                        <!-- Popis -->
                        <div class="prose max-w-none">
                            <h3 class="text-lg font-medium text-gray-900 mb-3">Popis</h3>
                            <div class="text-gray-700 whitespace-pre-line">{{ post.description }}</div>
                        </div>
                    </div>

                    <!-- Sidebar s informacemi -->
                    <div class="lg:col-span-1">
                        <div class="bg-gray-50 rounded-lg p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Informace o inzerátu</h3>

                            <dl class="space-y-3">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Kategorie</dt>
                                    <dd class="text-sm text-gray-900">{{ post.category.name }}</dd>
                                </div>

                                {% if post.breed %}
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Plemeno</dt>
                                        <dd class="text-sm text-gray-900">{{ post.breed.name }}</dd>
                                    </div>
                                {% endif %}

                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Lokalita</dt>
                                    <dd class="text-sm text-gray-900">
                                        {{ post.region ? post.region.name : post.country.label }}
                                    </dd>
                                </div>

                                {% if post.contactPhone %}
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Kontakt</dt>
                                        <dd class="text-sm text-gray-900">
                                            <a href="tel:{{ post.contactPhone }}" class="text-indigo-600 hover:text-indigo-500">
                                                {{ post.contactPhone }}
                                            </a>
                                        </dd>
                                    </div>
                                {% endif %}

                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Země</dt>
                                    <dd class="text-sm text-gray-900">{{ post.country.label }}</dd>
                                </div>
                            </dl>

                            {% if post.contactPhone %}
                                <div class="mt-6">
                                    <a href="tel:{{ post.contactPhone }}"
                                       class="w-full bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium transition duration-150 ease-in-out text-center block">
                                        📞 Zavolat
                                    </a>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
